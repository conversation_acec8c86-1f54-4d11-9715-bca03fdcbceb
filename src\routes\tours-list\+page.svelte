<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import Navbar from '../../components/Navbar.svelte';
  import Footer from '../../components/Footer.svelte';

  // State variables
  let categories = [];
  let destinations = [];
  let selectedCategory = null;
  let selectedDestination = null;
  let tours = [];
  let isLoading = true;
  let error = null;
  let showMoreTours = false;

  // Format price to VND
  function formatPrice(price) {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  }

  // Format date to DD/MM/YYYY
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  }

  // Calculate tour duration in days
  function calculateDuration(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }

  // Fetch all data on component mount
  async function fetchInitialData() {
    isLoading = true;
    error = null;

    try {
      // Fetch all tours first to get destinations
      const toursResponse = await fetch('http://localhost:5000/api/tours');
      let allTours = [];

      if (toursResponse.ok) {
        const toursData = await toursResponse.json();
        allTours = toursData.data || toursData || [];
      }

      // Extract unique destinations
      const uniqueDestinations = new Set();
      allTours.forEach(tour => {
        if (tour.diem_den) {
          uniqueDestinations.add(tour.diem_den);
        }
        if (tour.dia_diem) {
          uniqueDestinations.add(tour.dia_diem);
        }
      });
      destinations = Array.from(uniqueDestinations).sort();

      // Create predefined categories
      categories = [
        {
          ma_phan_loai: 'all',
          ten_phan_loai: 'Tất cả tour',
          mo_ta: 'Tất cả các tour du lịch',
          icon: 'fas fa-globe',
          tours: allTours
        },
        {
          ma_phan_loai: 'trong_nuoc',
          ten_phan_loai: 'Tour trong nước',
          mo_ta: 'Khám phá vẻ đẹp Việt Nam',
          icon: 'fas fa-map-marked-alt',
          tours: allTours.filter(tour => tour.loai_tour === 'trong_nuoc')
        },
        {
          ma_phan_loai: 'nuoc_ngoai',
          ten_phan_loai: 'Tour nước ngoài',
          mo_ta: 'Trải nghiệm thế giới rộng lớn',
          icon: 'fas fa-plane',
          tours: allTours.filter(tour => tour.loai_tour === 'nuoc_ngoai')
        }
      ];

      // Fetch additional categories from API
      try {
        const categoriesResponse = await fetch('http://localhost:5000/api/phan-loai-tour');
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          const apiCategories = categoriesData.data || [];

          // Fetch tours for each API category
          for (const category of apiCategories) {
            try {
              const categoryToursResponse = await fetch(`http://localhost:5000/api/phan-loai-tour/${category.ma_phan_loai}/tours`);
              if (categoryToursResponse.ok) {
                const categoryToursData = await categoryToursResponse.json();
                categories.push({
                  ...category,
                  icon: 'fas fa-tags',
                  tours: categoryToursData.data || []
                });
              }
            } catch (err) {
              console.error(`Lỗi khi tải tour cho danh mục ${category.ten_phan_loai}:`, err);
            }
          }
        }
      } catch (err) {
        console.error('Lỗi khi tải danh mục từ API:', err);
      }

      // Set default selected category
      if (categories.length > 0) {
        selectedCategory = categories[0];
        tours = selectedCategory.tours;
      }

      isLoading = false;
    } catch (err) {
      console.error('Lỗi khi tải dữ liệu:', err);
      error = err.message;
      isLoading = false;
    }
  }

  // Handle category selection
  function selectCategory(category) {
    selectedCategory = category;
    selectedDestination = null; // Reset destination filter
    tours = category.tours;
    showMoreTours = false; // Reset show more state
  }

  // Handle destination selection
  function selectDestination(destination) {
    selectedDestination = destination;

    if (selectedCategory) {
      if (destination) {
        // Filter tours by destination
        tours = selectedCategory.tours.filter(tour =>
          tour.diem_den === destination || tour.dia_diem === destination
        );
      } else {
        // Show all tours in category
        tours = selectedCategory.tours;
      }
    }
    showMoreTours = false; // Reset show more state
  }

  // View tour details
  function viewTourDetails(tourId) {
    goto(`/tour/${tourId}`);
  }

  // Toggle show more tours
  function toggleShowMore() {
    showMoreTours = !showMoreTours;
  }

  // Get displayed tours (with show more functionality)
  $: displayedTours = showMoreTours ? tours : tours.slice(0, 6);

  // Initialize component
  onMount(fetchInitialData);
</script>

<svelte:head>
  <title>Danh Sách Tour Du Lịch</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</svelte:head>

<div class="tours-list-page">
  <Navbar />

  <div class="page-content">
    <div class="page-header">
      <h1>Danh Sách Tour Du Lịch</h1>
      <p>Khám phá các tour du lịch hấp dẫn của chúng tôi</p>
    </div>

    <div class="main-layout">
      <!-- Sidebar bên trái -->
      <div class="sidebar">
        <!-- Danh mục tour -->
        <div class="sidebar-section">
          <h3><i class="fas fa-list"></i> Danh mục tour</h3>
          <div class="categories-list">
            {#each categories as category (category.ma_phan_loai)}
              <button
                class="category-item {selectedCategory?.ma_phan_loai === category.ma_phan_loai ? 'active' : ''}"
                on:click={() => selectCategory(category)}
              >
                <i class="{category.icon}"></i>
                <span>{category.ten_phan_loai}</span>
                <span class="tour-count">({category.tours.length})</span>
              </button>
            {/each}
          </div>
        </div>

        <!-- Danh sách điểm đến -->
        <div class="sidebar-section">
          <h3><i class="fas fa-map-marker-alt"></i> Điểm đến</h3>
          <div class="destinations-list">
            <button
              class="destination-item {selectedDestination === null ? 'active' : ''}"
              on:click={() => selectDestination(null)}
            >
              <span>Tất cả điểm đến</span>
            </button>
            {#each destinations as destination}
              <button
                class="destination-item {selectedDestination === destination ? 'active' : ''}"
                on:click={() => selectDestination(destination)}
              >
                <span>{destination}</span>
              </button>
            {/each}
          </div>
        </div>
      </div>

      <!-- Content bên phải -->
      <div class="content">
        {#if isLoading}
          <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>Đang tải danh sách tour...</p>
          </div>
        {:else if error}
          <div class="error-container">
            <p class="error-message">{error}</p>
            <button on:click={fetchInitialData}>Thử lại</button>
          </div>
        {:else if selectedCategory}
          <!-- Tên danh mục được chọn -->
          <div class="category-header">
            <h1>
              <i class="{selectedCategory.icon}"></i>
              {selectedCategory.ten_phan_loai}
              {#if selectedDestination}
                - {selectedDestination}
              {/if}
            </h1>
            <p>{selectedCategory.mo_ta}</p>
            <div class="tour-stats">
              <span class="tour-count">Tìm thấy {tours.length} tour</span>
            </div>
          </div>

          {#if tours.length === 0}
            <div class="no-tours">
              <i class="fas fa-search"></i>
              <h3>Không tìm thấy tour nào</h3>
              <p>Thử chọn danh mục khác hoặc điểm đến khác</p>
            </div>
          {:else}
            <!-- Danh sách tour theo chiều dọc -->
            <div class="tours-vertical-list">
              {#each displayedTours as tour (tour.ma_tour)}
                <div class="tour-item-vertical">
                  <div class="tour-image">
                    <button
                      class="image-button"
                      on:click={() => viewTourDetails(tour.ma_tour)}
                      aria-label={`Xem chi tiết tour ${tour.ten_tour}`}
                    >
                      <img
                        src={tour.hinh_anh ? `/images/${tour.hinh_anh}` : '/images/default-tour.svg'}
                        alt={tour.ten_tour}
                      />
                    </button>
                    <div class="tour-type">
                      {tour.loai_tour === 'trong_nuoc' ? 'Trong nước' : 'Quốc tế'}
                    </div>
                  </div>

                  <div class="tour-details">
                    <button
                      class="tour-title-button"
                      on:click={() => viewTourDetails(tour.ma_tour)}
                      aria-label={`Xem chi tiết tour ${tour.ten_tour}`}
                    >
                      <h3 class="tour-title">{tour.ten_tour}</h3>
                    </button>

                    <div class="tour-info-grid">
                      <div class="info-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>{tour.diem_di} → {tour.diem_den}</span>
                      </div>

                      <div class="info-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>{formatDate(tour.ngay_bat_dau)} - {formatDate(tour.ngay_ket_thuc)}</span>
                      </div>

                      <div class="info-item">
                        <i class="fas fa-clock"></i>
                        <span>{calculateDuration(tour.ngay_bat_dau, tour.ngay_ket_thuc)} ngày</span>
                      </div>

                      <div class="info-item">
                        <i class="fas fa-users"></i>
                        <span>Còn {tour.so_cho_trong} chỗ</span>
                      </div>
                    </div>

                    {#if tour.mo_ta}
                      <p class="tour-description">{tour.mo_ta.substring(0, 150)}...</p>
                    {/if}

                    <div class="tour-footer">
                      <div class="tour-price">
                        <span class="price">{formatPrice(tour.gia)}</span>
                        <span class="price-per">/người</span>
                      </div>

                      <button
                        class="view-details-btn"
                        on:click={() => viewTourDetails(tour.ma_tour)}
                        disabled={tour.so_cho_trong <= 0}
                      >
                        {tour.so_cho_trong > 0 ? 'Xem thêm' : 'Hết chỗ'}
                      </button>
                    </div>
                  </div>
                </div>
              {/each}

              <!-- Nút xem thêm -->
              {#if tours.length > 6}
                <div class="show-more-container">
                  <button class="show-more-btn" on:click={toggleShowMore}>
                    {showMoreTours ? 'Thu gọn' : `Xem thêm ${tours.length - 6} tour`}
                    <i class="fas {showMoreTours ? 'fa-chevron-up' : 'fa-chevron-down'}"></i>
                  </button>
                </div>
              {/if}
            </div>
          {/if}
        {/if}
      </div>
    </div>
  </div>

  <Footer />
</div>

<style>
  .tours-list-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
  }

  .page-content {
    flex: 1;
    padding: 2rem 0;
  }

  .page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 0 1rem;
  }

  .page-header h1 {
    font-size: 2.5rem;
    color: #343a40;
    margin-bottom: 0.5rem;
  }

  .page-header p {
    font-size: 1.1rem;
    color: #6c757d;
  }

  /* Main Layout */
  .main-layout {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    align-items: start;
  }

  /* Sidebar */
  .sidebar {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 2rem;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
  }

  .sidebar-section {
    margin-bottom: 2rem;
  }

  .sidebar-section:last-child {
    margin-bottom: 0;
  }

  .sidebar-section h3 {
    color: #343a40;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .sidebar-section h3 i {
    color: #007bff;
  }

  /* Categories List */
  .categories-list,
  .destinations-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .category-item,
  .destination-item {
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: #f8f9fa;
    color: #495057;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .category-item:hover,
  .destination-item:hover {
    background: #e9ecef;
    color: #007bff;
  }

  .category-item.active,
  .destination-item.active {
    background: #007bff;
    color: white;
  }

  .category-item i {
    min-width: 1.2rem;
    text-align: center;
  }

  .tour-count {
    margin-left: auto;
    font-size: 0.8rem;
    opacity: 0.8;
  }

  /* Content Area */
  .content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-height: 600px;
  }

  /* Category Header */
  .category-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #e9ecef;
  }

  .category-header h1 {
    color: #343a40;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .category-header h1 i {
    color: #007bff;
    font-size: 1.8rem;
  }

  .category-header p {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  .tour-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .tour-stats .tour-count {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
  }

  /* Loading and Error States */
  .loading-container,
  .error-container {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
  }

  .loading-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-container button {
    padding: 0.75rem 1.5rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.2s;
  }

  .error-container button:hover {
    background: #0056b3;
  }

  /* No Tours State */
  .no-tours {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
  }

  .no-tours i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dee2e6;
  }

  .no-tours h3 {
    margin-bottom: 0.5rem;
    color: #495057;
  }

  /* Tours Vertical List */
  .tours-vertical-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .tour-item-vertical {
    display: flex;
    gap: 1.5rem;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: #fafafa;
  }

  .tour-item-vertical:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    background: white;
  }

  /* Tour Image */
  .tour-image {
    position: relative;
    flex-shrink: 0;
    width: 250px;
    height: 180px;
    border-radius: 8px;
    overflow: hidden;
  }

  .image-button {
    width: 100%;
    height: 100%;
    border: none;
    padding: 0;
    background: none;
    cursor: pointer;
    overflow: hidden;
  }

  .tour-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .image-button:hover img {
    transform: scale(1.05);
  }

  .tour-type {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
  }

  /* Tour Details */
  .tour-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .tour-title-button {
    background: none;
    border: none;
    padding: 0;
    text-align: left;
    cursor: pointer;
    transition: color 0.2s ease;
  }

  .tour-title-button:hover {
    color: #007bff;
  }

  .tour-title {
    font-size: 1.4rem;
    color: #343a40;
    margin: 0;
    font-weight: 600;
    line-height: 1.3;
  }

  .tour-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.9rem;
  }

  .info-item i {
    color: #007bff;
    min-width: 1rem;
    text-align: center;
  }

  .tour-description {
    color: #6c757d;
    line-height: 1.5;
    margin: 0;
  }

  .tour-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
  }

  .tour-price {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
  }

  .price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #dc3545;
  }

  .price-per {
    color: #6c757d;
    font-size: 0.9rem;
  }

  .view-details-btn {
    padding: 0.75rem 1.5rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .view-details-btn:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-1px);
  }

  .view-details-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
  }

  /* Show More Button */
  .show-more-container {
    text-align: center;
    margin-top: 2rem;
  }

  .show-more-btn {
    padding: 1rem 2rem;
    background: transparent;
    color: #007bff;
    border: 2px solid #007bff;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
  }

  .show-more-btn:hover {
    background: #007bff;
    color: white;
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .main-layout {
      grid-template-columns: 280px 1fr;
      gap: 1.5rem;
    }
  }

  @media (max-width: 992px) {
    .main-layout {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .sidebar {
      position: static;
      max-height: none;
    }

    .tour-item-vertical {
      flex-direction: column;
      gap: 1rem;
    }

    .tour-image {
      width: 100%;
      height: 200px;
    }

    .tour-info-grid {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }
  }

  @media (max-width: 768px) {
    .page-header h1 {
      font-size: 2rem;
    }

    .page-content {
      padding: 1rem 0;
    }

    .main-layout {
      padding: 0 0.5rem;
    }

    .sidebar,
    .content {
      padding: 1rem;
    }

    .category-header h1 {
      font-size: 1.5rem;
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .tour-footer {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }

    .view-details-btn {
      width: 100%;
    }
  }

  @media (max-width: 480px) {
    .tour-item-vertical {
      padding: 1rem;
    }

    .tour-image {
      height: 150px;
    }

    .price {
      font-size: 1.2rem;
    }
  }
</style>