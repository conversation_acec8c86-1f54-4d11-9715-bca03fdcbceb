<script>
  import { createEventDispatcher } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  let searchQuery = '';
  let destination = '';
  let checkIn = '';
  let checkOut = '';
  let guests = 1;
  let searchType = 'tours'; // 'tours' hoặc 'hotels'
  
  function handleSearch() {
    if (!searchQuery.trim() && !destination.trim()) {
      alert('Vui lòng nhập từ khóa tìm kiếm hoặc điểm đến');
      return;
    }
    
    const searchData = {
      query: searchQuery,
      destination,
      checkIn,
      checkOut,
      guests,
      type: searchType
    };
    
    dispatch('search', searchData);
  }
  
  function handleSearchTypeChange(type) {
    searchType = type;
    // Reset form khi đổi loại tìm kiếm
    searchQuery = '';
    destination = '';
    checkIn = '';
    checkOut = '';
    guests = 1;
  }
</script>

<section class="search-hero">
  <div class="hero-background">
    <div class="overlay"></div>
  </div>
  
  <div class="hero-content">
    <div class="container">
      <div class="hero-text">
        <h1>Khám Phá Thế Giới Cùng Chúng Tôi</h1>
        <p>Tìm kiếm những chuyến du lịch tuyệt vời và khách sạn lý tưởng cho kỳ nghỉ của bạn</p>
      </div>
      
      <div class="search-container">
        <!-- Tab chọn loại tìm kiếm -->
        <div class="search-tabs">
          <button 
            class="tab-btn {searchType === 'tours' ? 'active' : ''}"
            on:click={() => handleSearchTypeChange('tours')}
          >
            <i class="fas fa-map-marked-alt"></i>
            Tours Du Lịch
          </button>
          <button 
            class="tab-btn {searchType === 'hotels' ? 'active' : ''}"
            on:click={() => handleSearchTypeChange('hotels')}
          >
            <i class="fas fa-hotel"></i>
            Khách Sạn
          </button>
        </div>
        
        <!-- Form tìm kiếm -->
        <div class="search-form">
          <div class="form-row">
            <div class="form-group main-search">
              <label for="search-input">
                <i class="fas fa-search"></i>
              </label>
              <input
                id="search-input"
                type="text"
                bind:value={searchQuery}
                placeholder={searchType === 'tours' ? 'Tìm kiếm tour, địa điểm...' : 'Tìm kiếm khách sạn...'}
                class="search-input"
              />
            </div>
            
            <div class="form-group">
              <label for="destination">
                <i class="fas fa-map-marker-alt"></i>
              </label>
              <input
                id="destination"
                type="text"
                bind:value={destination}
                placeholder="Điểm đến"
                class="form-input"
              />
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="check-in">
                <i class="fas fa-calendar-alt"></i>
              </label>
              <input
                id="check-in"
                type="date"
                bind:value={checkIn}
                class="form-input"
              />
              <span class="input-label">Ngày đi</span>
            </div>
            
            <div class="form-group">
              <label for="check-out">
                <i class="fas fa-calendar-check"></i>
              </label>
              <input
                id="check-out"
                type="date"
                bind:value={checkOut}
                class="form-input"
              />
              <span class="input-label">Ngày về</span>
            </div>
            
            <div class="form-group">
              <label for="guests">
                <i class="fas fa-users"></i>
              </label>
              <select
                id="guests"
                bind:value={guests}
                class="form-input"
              >
                {#each Array(10) as _, i}
                  <option value={i + 1}>{i + 1} người</option>
                {/each}
              </select>
              <span class="input-label">Số người</span>
            </div>
          </div>
          
          <div class="form-row">
            <button class="search-btn" on:click={handleSearch}>
              <i class="fas fa-search"></i>
              Tìm Kiếm
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  .search-hero {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }
  
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
      #667eea 0%, 
      #764ba2 25%, 
      #f093fb 50%, 
      #f5576c 75%, 
      #4facfe 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
  }
  
  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
  }
  
  .hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: 2rem 0;
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  .hero-text {
    text-align: center;
    color: white;
    margin-bottom: 3rem;
  }
  
  .hero-text h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
  
  .hero-text p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }
  
  .search-container {
    max-width: 900px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
  
  .search-tabs {
    display: flex;
    margin-bottom: 2rem;
    border-radius: 12px;
    overflow: hidden;
    background: #f8f9fa;
  }
  
  .tab-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    color: #6c757d;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .tab-btn:hover {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
  }
  
  .tab-btn.active {
    background: #007bff;
    color: white;
  }
  
  .search-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .form-row {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }
  
  .form-group {
    position: relative;
    flex: 1;
    min-width: 200px;
  }
  
  .form-group.main-search {
    flex: 2;
    min-width: 300px;
  }
  
  .form-group label {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 1;
  }
  
  .form-input,
  .search-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
  }
  
  .form-input:focus,
  .search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
  
  .input-label {
    position: absolute;
    bottom: -1.5rem;
    left: 0;
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
  }
  
  .search-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 1rem 3rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    align-self: center;
    min-width: 200px;
  }
  
  .search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 123, 255, 0.3);
  }
  
  .search-btn:active {
    transform: translateY(0);
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .hero-text h1 {
      font-size: 2rem;
    }
    
    .hero-text p {
      font-size: 1rem;
    }
    
    .search-container {
      margin: 0 1rem;
      padding: 1.5rem;
    }
    
    .form-row {
      flex-direction: column;
    }
    
    .form-group {
      min-width: auto;
    }
    
    .search-tabs {
      flex-direction: column;
    }
    
    .tab-btn {
      padding: 0.8rem 1rem;
    }
  }
  
  @media (max-width: 480px) {
    .search-hero {
      min-height: 60vh;
    }
    
    .hero-text h1 {
      font-size: 1.5rem;
    }
    
    .search-container {
      padding: 1rem;
    }
    
    .form-input,
    .search-input {
      padding: 0.8rem 0.8rem 0.8rem 2.5rem;
    }
    
    .search-btn {
      padding: 0.8rem 2rem;
      min-width: auto;
      width: 100%;
    }
  }
</style>
